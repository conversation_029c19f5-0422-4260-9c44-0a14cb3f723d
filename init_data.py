"""
ملف لإنشاء بيانات تجريبية للنظام
"""
from app import app
from models import db, Student, Class, Subject, Payment, Grade, Attendance
from datetime import date, datetime, timedelta
import random

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    with app.app_context():
        # إنشاء قاعدة البيانات
        db.create_all()
        
        # التحقق من وجود بيانات مسبقة
        if Class.query.count() > 0:
            print("البيانات موجودة مسبقاً!")
            return
        
        print("إنشاء البيانات التجريبية...")
        
        # إنشاء الصفوف
        classes_data = [
            {"name": "الصف الأول الابتدائي", "level": "ابتدائي", "monthly_fee": 5000, "annual_fee": 50000},
            {"name": "الصف الثاني الابتدائي", "level": "ابتدائي", "monthly_fee": 5500, "annual_fee": 55000},
            {"name": "الصف الثالث الابتدائي", "level": "ابتدائي", "monthly_fee": 6000, "annual_fee": 60000},
            {"name": "الصف الأول المتوسط", "level": "متوسط", "monthly_fee": 7000, "annual_fee": 70000},
            {"name": "الصف الثاني المتوسط", "level": "متوسط", "monthly_fee": 7500, "annual_fee": 75000},
            {"name": "الصف الأول الثانوي", "level": "ثانوي", "monthly_fee": 8000, "annual_fee": 80000},
        ]
        
        classes = []
        for class_data in classes_data:
            class_obj = Class(
                name=class_data["name"],
                level=class_data["level"],
                academic_year="2024-2025",
                monthly_fee=class_data["monthly_fee"],
                annual_fee=class_data["annual_fee"],
                max_students=30
            )
            db.session.add(class_obj)
            classes.append(class_obj)
        
        db.session.commit()
        print(f"تم إنشاء {len(classes)} صف دراسي")
        
        # إنشاء المواد
        subjects_data = {
            "ابتدائي": ["الرياضيات", "اللغة العربية", "اللغة الفرنسية", "التربية الإسلامية", "التاريخ والجغرافيا"],
            "متوسط": ["الرياضيات", "اللغة العربية", "اللغة الفرنسية", "اللغة الإنجليزية", "العلوم الطبيعية", "التاريخ والجغرافيا", "التربية الإسلامية"],
            "ثانوي": ["الرياضيات", "الفيزياء", "الكيمياء", "العلوم الطبيعية", "اللغة العربية", "اللغة الفرنسية", "اللغة الإنجليزية", "الفلسفة"]
        }
        
        subjects = []
        for class_obj in classes:
            for i, subject_name in enumerate(subjects_data[class_obj.level]):
                subject = Subject(
                    name=subject_name,
                    code=f"{class_obj.level[:2]}{i+1:02d}",
                    class_id=class_obj.id,
                    teacher_name=f"الأستاذ {subject_name}"
                )
                db.session.add(subject)
                subjects.append(subject)
        
        db.session.commit()
        print(f"تم إنشاء {len(subjects)} مادة دراسية")
        
        # إنشاء الطلاب
        first_names = ["أحمد", "محمد", "علي", "حسن", "يوسف", "عبدالله", "إبراهيم", "عمر", "خالد", "سعد",
                      "فاطمة", "عائشة", "خديجة", "مريم", "زينب", "سارة", "نور", "هدى", "أمينة", "ليلى"]
        last_names = ["بن علي", "بن محمد", "بن أحمد", "بن يوسف", "بن عبدالله", "العربي", "المغربي", "التونسي", "الجزائري", "المصري"]
        
        students = []
        for class_obj in classes:
            # إنشاء 8-15 طالب لكل صف
            num_students = random.randint(8, 15)
            for i in range(num_students):
                first_name = random.choice(first_names)
                last_name = random.choice(last_names)
                
                # تحديد الجنس بناءً على الاسم
                female_names = ["فاطمة", "عائشة", "خديجة", "مريم", "زينب", "سارة", "نور", "هدى", "أمينة", "ليلى"]
                gender = "أنثى" if first_name in female_names else "ذكر"
                
                # تاريخ ميلاد عشوائي
                birth_year = 2024 - random.randint(6, 18)  # عمر بين 6-18 سنة
                birth_date = date(birth_year, random.randint(1, 12), random.randint(1, 28))
                
                student = Student(
                    first_name=first_name,
                    last_name=last_name,
                    date_of_birth=birth_date,
                    gender=gender,
                    address=f"حي {random.choice(['النصر', 'السلام', 'الأمل', 'الوحدة', 'التحرير'])}, الجزائر",
                    phone=f"055{random.randint(1000000, 9999999)}",
                    parent_phone=f"055{random.randint(1000000, 9999999)}",
                    parent_name=f"{random.choice(['أحمد', 'محمد', 'علي', 'حسن', 'يوسف'])} {last_name}",
                    class_id=class_obj.id,
                    enrollment_date=date(2024, 9, 1),
                    is_active=True
                )
                db.session.add(student)
                students.append(student)
        
        db.session.commit()
        print(f"تم إنشاء {len(students)} طالب")
        
        # إنشاء بعض المدفوعات
        payments = []
        for student in students[:20]:  # أول 20 طالب فقط
            # دفعة شهرية
            payment = Payment(
                student_id=student.id,
                amount=student.student_class.monthly_fee,
                payment_type='monthly',
                payment_date=date(2024, 10, random.randint(1, 30)),
                due_date=date(2024, 10, 30),
                status=random.choice(['paid', 'pending', 'overdue']),
                month=10,
                year=2024,
                notes="دفعة شهر أكتوبر"
            )
            db.session.add(payment)
            payments.append(payment)
        
        db.session.commit()
        print(f"تم إنشاء {len(payments)} دفعة")
        
        # إنشاء بعض الدرجات
        grades = []
        for student in students[:15]:  # أول 15 طالب فقط
            class_subjects = Subject.query.filter_by(class_id=student.class_id).all()
            for subject in class_subjects[:3]:  # أول 3 مواد فقط
                grade = Grade(
                    student_id=student.id,
                    subject_id=subject.id,
                    exam_type="امتحان شهري",
                    grade=random.randint(10, 20),
                    max_grade=20.0,
                    exam_date=date(2024, 10, 15),
                    semester="الفصل الأول",
                    academic_year="2024-2025"
                )
                db.session.add(grade)
                grades.append(grade)
        
        db.session.commit()
        print(f"تم إنشاء {len(grades)} درجة")
        
        # إنشاء بعض سجلات الحضور
        attendances = []
        for student in students[:10]:  # أول 10 طلاب فقط
            for day in range(1, 8):  # أسبوع واحد
                attendance = Attendance(
                    student_id=student.id,
                    date=date(2024, 10, day),
                    status=random.choice(['present', 'absent', 'late']),
                    notes=""
                )
                db.session.add(attendance)
                attendances.append(attendance)
        
        db.session.commit()
        print(f"تم إنشاء {len(attendances)} سجل حضور")
        
        print("تم إنشاء جميع البيانات التجريبية بنجاح!")

if __name__ == '__main__':
    create_sample_data()
