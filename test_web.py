"""
اختبار النظام عبر الويب
"""
from app import app, db
from models import Student, Class
from datetime import date

def test_web_interface():
    """اختبار واجهة الويب"""
    
    # تشغيل التطبيق في وضع الاختبار
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False  # تعطيل CSRF للاختبار
    
    with app.test_client() as client:
        with app.app_context():
            # اختبار الصفحة الرئيسية
            print("🌐 اختبار الصفحة الرئيسية...")
            response = client.get('/')
            if response.status_code == 200:
                print("✅ الصفحة الرئيسية تعمل")
            else:
                print(f"❌ خطأ في الصفحة الرئيسية: {response.status_code}")
            
            # اختبار صفحة قائمة الطلاب
            print("\n📚 اختبار صفحة الطلاب...")
            response = client.get('/students')
            if response.status_code == 200:
                print("✅ صفحة الطلاب تعمل")
            else:
                print(f"❌ خطأ في صفحة الطلاب: {response.status_code}")
            
            # اختبار صفحة إضافة طالب (GET)
            print("\n➕ اختبار صفحة إضافة طالب...")
            response = client.get('/students/add')
            if response.status_code == 200:
                print("✅ صفحة إضافة طالب تعمل")
            else:
                print(f"❌ خطأ في صفحة إضافة طالب: {response.status_code}")
            
            # التحقق من وجود صفوف
            classes = Class.query.all()
            if not classes:
                print("⚠️ لا توجد صفوف - سيتم إنشاء صف تجريبي")
                test_class = Class(
                    name="صف تجريبي",
                    level="ابتدائي",
                    academic_year="2024-2025",
                    monthly_fee=5000,
                    annual_fee=50000,
                    max_students=30
                )
                db.session.add(test_class)
                db.session.commit()
                classes = [test_class]
            
            # اختبار إضافة طالب (POST)
            print("\n📝 اختبار إضافة طالب...")
            student_data = {
                'first_name': 'اختبار',
                'last_name': 'ويب',
                'date_of_birth': '2010-05-15',
                'gender': 'ذكر',
                'address': 'عنوان تجريبي',
                'phone': '0551234567',
                'parent_phone': '0661234567',
                'parent_name': 'ولي أمر تجريبي',
                'class_id': str(classes[0].id),
                'enrollment_date': date.today().strftime('%Y-%m-%d'),
                'is_active': True
            }
            
            response = client.post('/students/add', data=student_data, follow_redirects=True)
            if response.status_code == 200:
                print("✅ تم إرسال بيانات الطالب")
                
                # التحقق من إضافة الطالب في قاعدة البيانات
                new_student = Student.query.filter_by(first_name='اختبار', last_name='ويب').first()
                if new_student:
                    print(f"✅ تم إضافة الطالب في قاعدة البيانات: {new_student.full_name}")
                else:
                    print("❌ لم يتم إضافة الطالب في قاعدة البيانات")
            else:
                print(f"❌ خطأ في إضافة الطالب: {response.status_code}")
                print(f"Response: {response.data.decode('utf-8')[:200]}...")

def check_database():
    """فحص قاعدة البيانات"""
    with app.app_context():
        print("🗄️ فحص قاعدة البيانات:")
        
        classes_count = Class.query.count()
        students_count = Student.query.count()
        
        print(f"   - عدد الصفوف: {classes_count}")
        print(f"   - عدد الطلاب: {students_count}")
        
        if classes_count == 0:
            print("⚠️ لا توجد صفوف - يجب إنشاء صفوف أولاً")
            return False
        
        return True

if __name__ == '__main__':
    print("🧪 اختبار النظام عبر الويب")
    print("=" * 50)
    
    # فحص قاعدة البيانات
    if not check_database():
        print("\n❌ يجب تشغيل init_data.py أولاً لإنشاء البيانات التجريبية")
        exit(1)
    
    # اختبار واجهة الويب
    print("\n🌐 اختبار واجهة الويب:")
    test_web_interface()
    
    print("\n" + "=" * 50)
    print("انتهى الاختبار")
