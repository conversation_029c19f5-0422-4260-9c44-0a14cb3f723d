from flask import Flask, render_template, request, redirect, url_for, flash

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///school_management.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize db first
from models import db
db.init_app(app)

# Import models after db initialization
from models import Student, Class, Payment, Grade, Attendance, Subject
from forms import StudentForm, ClassForm, PaymentForm, GradeForm, AttendanceForm, SearchForm

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    total_students = Student.query.count()
    total_classes = Class.query.count()
    pending_payments = Payment.query.filter_by(status='pending').count()

    return render_template('index.html',
                         total_students=total_students,
                         total_classes=total_classes,
                         pending_payments=pending_payments)

# ==================== إدارة الطلاب ====================

@app.route('/students')
def students_list():
    """عرض قائمة الطلاب"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    class_filter = request.args.get('class', '', type=str)

    query = Student.query

    if search:
        query = query.filter(
            (Student.first_name.contains(search)) |
            (Student.last_name.contains(search)) |
            (Student.parent_name.contains(search))
        )

    if class_filter:
        query = query.filter_by(class_id=class_filter)

    students = query.paginate(
        page=page, per_page=10, error_out=False
    )

    classes = Class.query.all()

    return render_template('students/list.html',
                         students=students,
                         classes=classes,
                         search=search,
                         class_filter=class_filter)

@app.route('/students/add', methods=['GET', 'POST'])
def add_student():
    """إضافة طالب جديد"""
    form = StudentForm()
    form.class_id.choices = [(c.id, c.name) for c in Class.query.all()]

    if form.validate_on_submit():
        student = Student(
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            date_of_birth=form.date_of_birth.data,
            gender=form.gender.data,
            address=form.address.data,
            phone=form.phone.data,
            parent_phone=form.parent_phone.data,
            parent_name=form.parent_name.data,
            class_id=form.class_id.data,
            enrollment_date=form.enrollment_date.data,
            is_active=form.is_active.data
        )

        db.session.add(student)
        db.session.commit()

        flash('تم إضافة الطالب بنجاح!', 'success')
        return redirect(url_for('students_list'))

    return render_template('students/form.html', form=form, title='إضافة طالب جديد')

@app.route('/students/<int:id>')
def student_detail(id):
    """عرض تفاصيل الطالب"""
    student = Student.query.get_or_404(id)
    return render_template('students/detail.html', student=student)

@app.route('/students/<int:id>/edit', methods=['GET', 'POST'])
def edit_student(id):
    """تعديل بيانات الطالب"""
    student = Student.query.get_or_404(id)
    form = StudentForm(obj=student)
    form.class_id.choices = [(c.id, c.name) for c in Class.query.all()]

    if form.validate_on_submit():
        form.populate_obj(student)
        db.session.commit()

        flash('تم تحديث بيانات الطالب بنجاح!', 'success')
        return redirect(url_for('student_detail', id=student.id))

    return render_template('students/form.html', form=form, title='تعديل بيانات الطالب', student=student)

@app.route('/students/<int:id>/delete', methods=['POST'])
def delete_student(id):
    """حذف الطالب"""
    student = Student.query.get_or_404(id)

    # التحقق من وجود بيانات مرتبطة
    if student.payments or student.grades or student.attendances:
        flash('لا يمكن حذف الطالب لوجود بيانات مرتبطة به!', 'error')
        return redirect(url_for('student_detail', id=id))

    db.session.delete(student)
    db.session.commit()

    flash('تم حذف الطالب بنجاح!', 'success')
    return redirect(url_for('students_list'))

# ==================== إدارة الصفوف ====================

@app.route('/classes')
def classes_list():
    """عرض قائمة الصفوف"""
    classes = Class.query.all()
    return render_template('classes/list.html', classes=classes)

@app.route('/classes/add', methods=['GET', 'POST'])
def add_class():
    """إضافة صف جديد"""
    from forms import ClassForm
    form = ClassForm()

    if form.validate_on_submit():
        class_obj = Class(
            name=form.name.data,
            level=form.level.data,
            academic_year=form.academic_year.data,
            monthly_fee=form.monthly_fee.data,
            annual_fee=form.annual_fee.data,
            max_students=form.max_students.data
        )

        db.session.add(class_obj)
        db.session.commit()

        flash('تم إضافة الصف بنجاح!', 'success')
        return redirect(url_for('classes_list'))

    return render_template('classes/form.html', form=form, title='إضافة صف جديد')

@app.route('/classes/<int:id>')
def class_detail(id):
    """عرض تفاصيل الصف"""
    class_obj = Class.query.get_or_404(id)
    return render_template('classes/detail.html', class_obj=class_obj)

@app.route('/classes/<int:id>/edit', methods=['GET', 'POST'])
def edit_class(id):
    """تعديل بيانات الصف"""
    from forms import ClassForm
    class_obj = Class.query.get_or_404(id)
    form = ClassForm(obj=class_obj)

    if form.validate_on_submit():
        form.populate_obj(class_obj)
        db.session.commit()

        flash('تم تحديث بيانات الصف بنجاح!', 'success')
        return redirect(url_for('class_detail', id=class_obj.id))

    return render_template('classes/form.html', form=form, title='تعديل بيانات الصف', class_obj=class_obj)

# ==================== إدارة المدفوعات ====================

@app.route('/payments')
def payments_list():
    """عرض قائمة المدفوعات"""
    page = request.args.get('page', 1, type=int)
    status_filter = request.args.get('status', '', type=str)
    student_filter = request.args.get('student', '', type=str)

    query = Payment.query

    if status_filter:
        query = query.filter_by(status=status_filter)

    if student_filter:
        query = query.join(Student).filter(
            (Student.first_name.contains(student_filter)) |
            (Student.last_name.contains(student_filter))
        )

    payments = query.order_by(Payment.due_date.desc()).paginate(
        page=page, per_page=15, error_out=False
    )

    return render_template('payments/list.html', payments=payments,
                         status_filter=status_filter, student_filter=student_filter)

@app.route('/payments/add', methods=['GET', 'POST'])
def add_payment():
    """إضافة دفعة جديدة"""
    from forms import PaymentForm
    form = PaymentForm()
    form.student_id.choices = [(s.id, s.full_name) for s in Student.query.filter_by(is_active=True).all()]

    if form.validate_on_submit():
        payment = Payment(
            student_id=form.student_id.data,
            amount=form.amount.data,
            payment_type=form.payment_type.data,
            payment_date=form.payment_date.data,
            due_date=form.due_date.data,
            status=form.status.data,
            month=form.month.data,
            year=form.year.data,
            notes=form.notes.data
        )

        db.session.add(payment)
        db.session.commit()

        flash('تم إضافة الدفعة بنجاح!', 'success')
        return redirect(url_for('payments_list'))

    return render_template('payments/form.html', form=form, title='إضافة دفعة جديدة')

@app.route('/payments/<int:id>/edit', methods=['GET', 'POST'])
def edit_payment(id):
    """تعديل الدفعة"""
    from forms import PaymentForm
    payment = Payment.query.get_or_404(id)
    form = PaymentForm(obj=payment)
    form.student_id.choices = [(s.id, s.full_name) for s in Student.query.filter_by(is_active=True).all()]

    if form.validate_on_submit():
        form.populate_obj(payment)
        db.session.commit()

        flash('تم تحديث الدفعة بنجاح!', 'success')
        return redirect(url_for('payments_list'))

    return render_template('payments/form.html', form=form, title='تعديل الدفعة', payment=payment)

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True, host='0.0.0.0', port=5000)
