/* تصميم مخصص لنظام إدارة المدرسة */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-info: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
    
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
}

/* الإعدادات العامة */
body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* تحسين الشريط العلوي */
.navbar {
    background: var(--gradient-primary) !important;
    box-shadow: var(--shadow-md);
    border: none;
}

.navbar-brand {
    font-family: 'Amiri', serif;
    font-weight: 700;
    font-size: 1.8rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* تحسين الشريط الجانبي */
.sidebar {
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    box-shadow: var(--shadow-lg);
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.9);
    padding: 15px 20px;
    border-radius: var(--border-radius-sm);
    margin: 5px 10px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.sidebar .nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #fff;
    transform: translateX(-5px);
    box-shadow: var(--shadow-sm);
}

.sidebar .nav-link.active {
    background: var(--gradient-primary);
    color: #fff;
    box-shadow: var(--shadow-md);
}

/* تحسين البطاقات */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid var(--primary-color);
    font-weight: 600;
}

/* بطاقات الإحصائيات */
.stats-card {
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.stats-card-2 {
    background: var(--gradient-warning);
    color: white;
    border-radius: var(--border-radius);
}

.stats-card-3 {
    background: var(--gradient-info);
    color: white;
    border-radius: var(--border-radius);
}

.stats-card-4 {
    background: var(--gradient-success);
    color: white;
    border-radius: var(--border-radius);
}

.stats-card:hover,
.stats-card-2:hover,
.stats-card-3:hover,
.stats-card-4:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

/* تحسين الأزرار */
.btn {
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    padding: 10px 20px;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background: var(--gradient-primary);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: var(--gradient-success);
}

.btn-warning {
    background: var(--gradient-warning);
}

.btn-info {
    background: var(--gradient-info);
}

/* تحسين النماذج */
.form-control,
.form-select {
    border-radius: var(--border-radius-sm);
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

/* تحسين الجداول */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 15px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: scale(1.01);
}

/* تحسين الشارات */
.badge {
    border-radius: var(--border-radius-sm);
    padding: 6px 12px;
    font-weight: 500;
}

/* تحسين التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--shadow-sm);
    font-weight: 500;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

/* تحسين شريط التقدم */
.progress {
    border-radius: var(--border-radius-sm);
    height: 10px;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-bar {
    border-radius: var(--border-radius-sm);
    transition: width 0.6s ease;
}

/* تحسين التنقل */
.pagination .page-link {
    border-radius: var(--border-radius-sm);
    margin: 0 2px;
    border: none;
    color: var(--primary-color);
    font-weight: 500;
}

.pagination .page-item.active .page-link {
    background: var(--gradient-primary);
    border: none;
}

/* تحسين النوافذ المنبثقة */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid var(--primary-color);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

/* تحسين الأيقونات */
.fas, .far {
    transition: all 0.3s ease;
}

.btn:hover .fas,
.btn:hover .far {
    transform: scale(1.1);
}

/* تحسين الصور الرمزية */
.avatar-placeholder {
    background: var(--gradient-primary);
    border: 3px solid #fff;
    box-shadow: var(--shadow-md);
    font-weight: 700;
}

/* تحسين الخلفية */
.main-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    margin: 10px;
    padding: 20px;
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .sidebar {
        border-radius: 0;
    }
    
    .main-content {
        margin: 0;
        border-radius: 0;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }
}

/* تحسين الطباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .main-content {
        margin: 0;
        padding: 0;
        background: white;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.card,
.alert,
.table {
    animation: fadeIn 0.5s ease-out;
}

/* تحسين التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}
