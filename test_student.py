"""
اختبار إضافة طالب جديد
"""
from app import app, db
from models import Student, Class
from forms import StudentForm
from datetime import date

def test_add_student():
    """اختبار إضافة طالب"""
    with app.app_context():
        # التحقق من وجود صفوف
        classes = Class.query.all()
        if not classes:
            print("❌ لا توجد صفوف في النظام")
            return False
        
        print(f"✅ تم العثور على {len(classes)} صف")
        
        # إنشاء نموذج طالب
        form_data = {
            'first_name': 'أحمد',
            'last_name': 'محمد',
            'date_of_birth': date(2010, 5, 15),
            'gender': 'ذكر',
            'address': 'الجزائر العاصمة',
            'phone': '0551234567',
            'parent_phone': '0661234567',
            'parent_name': 'محمد أحمد',
            'class_id': classes[0].id,
            'enrollment_date': date.today(),
            'is_active': True
        }
        
        # إنشاء الطالب
        try:
            student = Student(**form_data)
            db.session.add(student)
            db.session.commit()
            
            print(f"✅ تم إضافة الطالب: {student.full_name}")
            print(f"   - الرقم: {student.id}")
            print(f"   - الصف: {student.student_class.name}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إضافة الطالب: {e}")
            db.session.rollback()
            return False

def test_form_validation():
    """اختبار التحقق من صحة النموذج"""
    with app.app_context():
        form = StudentForm()
        
        # تحديد خيارات الصفوف
        classes = Class.query.all()
        form.class_id.choices = [(c.id, c.name) for c in classes]
        
        print("📝 اختبار النموذج:")
        print(f"   - عدد الصفوف المتاحة: {len(form.class_id.choices)}")
        
        # بيانات تجريبية
        form.first_name.data = 'سارة'
        form.last_name.data = 'علي'
        form.date_of_birth.data = date(2012, 3, 20)
        form.gender.data = 'أنثى'
        form.parent_phone.data = '0771234567'
        form.parent_name.data = 'علي محمد'
        form.class_id.data = classes[0].id if classes else None
        form.enrollment_date.data = date.today()
        form.is_active.data = True
        
        # التحقق من صحة النموذج
        if form.validate():
            print("✅ النموذج صحيح")
            return True
        else:
            print("❌ أخطاء في النموذج:")
            for field, errors in form.errors.items():
                print(f"   - {field}: {errors}")
            return False

if __name__ == '__main__':
    print("🧪 اختبار نظام الطلاب")
    print("=" * 40)
    
    # اختبار إضافة طالب مباشرة
    print("\n1. اختبار إضافة طالب مباشرة:")
    test_add_student()
    
    # اختبار النموذج
    print("\n2. اختبار التحقق من النموذج:")
    test_form_validation()
    
    print("\n" + "=" * 40)
    print("انتهى الاختبار")
