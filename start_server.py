#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل نظام إدارة المدرسة
"""

import os
import sys
from app import app, db
from models import Student, Class, Subject, Payment, Grade, Attendance

def check_requirements():
    """التحقق من المتطلبات"""
    try:
        import flask
        import flask_sqlalchemy
        import flask_wtf
        import wtforms
        print("✅ جميع المتطلبات متوفرة")
        return True
    except ImportError as e:
        print(f"❌ خطأ في المتطلبات: {e}")
        print("يرجى تشغيل: pip install -r requirements.txt")
        return False

def initialize_database():
    """تهيئة قاعدة البيانات"""
    try:
        with app.app_context():
            # إنشاء الجداول
            db.create_all()
            
            # التحقق من وجود بيانات
            classes_count = Class.query.count()
            students_count = Student.query.count()
            payments_count = Payment.query.count()
            
            print(f"📊 إحصائيات قاعدة البيانات:")
            print(f"   - الصفوف: {classes_count}")
            print(f"   - الطلاب: {students_count}")
            print(f"   - المدفوعات: {payments_count}")
            
            if classes_count == 0:
                print("⚠️  لا توجد بيانات في النظام")
                print("💡 لإنشاء بيانات تجريبية، شغل: python init_data.py")
            
            return True
    except Exception as e:
        print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
        return False

def print_banner():
    """طباعة شعار النظام"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║                    🏫 نظام إدارة المدرسة                    ║
    ║                                                              ║
    ║                School Management System                      ║
    ║                                                              ║
    ║                    تم التطوير بـ Python & Flask              ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_system_info():
    """طباعة معلومات النظام"""
    print("🔧 معلومات النظام:")
    print(f"   - Python: {sys.version.split()[0]}")
    print(f"   - Flask: {app.config.get('FLASK_VERSION', 'غير محدد')}")
    print(f"   - قاعدة البيانات: SQLite")
    print(f"   - البيئة: {'تطوير' if app.debug else 'إنتاج'}")
    print()

def print_urls():
    """طباعة الروابط المتاحة"""
    print("🌐 الروابط المتاحة:")
    print("   - الصفحة الرئيسية: http://localhost:5000")
    print("   - إدارة الطلاب: http://localhost:5000/students")
    print("   - إدارة الصفوف: http://localhost:5000/classes")
    print("   - إدارة المدفوعات: http://localhost:5000/payments")
    print()

def print_instructions():
    """طباعة التعليمات"""
    print("📋 التعليمات:")
    print("   - للإيقاف: اضغط Ctrl+C")
    print("   - لإعادة التحميل: احفظ أي ملف Python")
    print("   - للمساعدة: راجع ملف README.md")
    print()

def main():
    """الدالة الرئيسية"""
    # طباعة الشعار
    print_banner()
    
    # التحقق من المتطلبات
    if not check_requirements():
        sys.exit(1)
    
    # طباعة معلومات النظام
    print_system_info()
    
    # تهيئة قاعدة البيانات
    if not initialize_database():
        sys.exit(1)
    
    # طباعة الروابط والتعليمات
    print_urls()
    print_instructions()
    
    # تشغيل الخادم
    try:
        print("🚀 بدء تشغيل الخادم...")
        print("=" * 60)
        
        # تحديد المنفذ
        port = int(os.environ.get('PORT', 5000))
        
        # تشغيل التطبيق
        app.run(
            host='0.0.0.0',
            port=port,
            debug=True,
            use_reloader=True,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n")
        print("🛑 تم إيقاف الخادم بواسطة المستخدم")
        print("👋 شكراً لاستخدام نظام إدارة المدرسة!")
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
