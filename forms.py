from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, IntegerField, SelectField, DateField, FloatField, TextAreaField, BooleanField, HiddenField
from wtforms.validators import DataRequired, Email, NumberRange, Length, Optional
from datetime import date

class StudentForm(FlaskForm):
    """نموذج إضافة/تعديل الطالب"""
    first_name = StringField('الاسم الأول', validators=[DataRequired(), Length(min=2, max=50)])
    last_name = StringField('اسم العائلة', validators=[DataRequired(), Length(min=2, max=50)])
    date_of_birth = DateField('تاريخ الميلاد', validators=[DataRequired()])
    gender = SelectField('الجنس', choices=[('ذكر', 'ذكر'), ('أنثى', 'أنثى')], validators=[DataRequired()])
    address = TextAreaField('العنوان', validators=[Optional(), Length(max=500)])
    phone = StringField('رقم الهاتف', validators=[Optional(), Length(max=20)])
    parent_phone = StringField('هاتف ولي الأمر', validators=[DataRequired(), Length(max=20)])
    parent_name = StringField('اسم ولي الأمر', validators=[DataRequired(), Length(min=2, max=100)])
    class_id = SelectField('الصف', coerce=int, validators=[DataRequired()])
    enrollment_date = DateField('تاريخ التسجيل', default=date.today, validators=[DataRequired()])
    is_active = BooleanField('نشط', default=True)

class ClassForm(FlaskForm):
    """نموذج إضافة/تعديل الصف"""
    name = StringField('اسم الصف', validators=[DataRequired(), Length(min=2, max=50)])
    level = SelectField('المرحلة', 
                       choices=[('ابتدائي', 'ابتدائي'), ('متوسط', 'متوسط'), ('ثانوي', 'ثانوي')], 
                       validators=[DataRequired()])
    academic_year = StringField('السنة الدراسية', validators=[DataRequired(), Length(max=10)])
    monthly_fee = FloatField('الرسوم الشهرية', validators=[DataRequired(), NumberRange(min=0)])
    annual_fee = FloatField('الرسوم السنوية', validators=[DataRequired(), NumberRange(min=0)])
    max_students = IntegerField('الحد الأقصى للطلاب', validators=[DataRequired(), NumberRange(min=1, max=100)])

class SubjectForm(FlaskForm):
    """نموذج إضافة/تعديل المادة"""
    name = StringField('اسم المادة', validators=[DataRequired(), Length(min=2, max=100)])
    code = StringField('رمز المادة', validators=[DataRequired(), Length(min=2, max=10)])
    class_id = SelectField('الصف', coerce=int, validators=[DataRequired()])
    teacher_name = StringField('اسم المدرس', validators=[Optional(), Length(max=100)])

class PaymentForm(FlaskForm):
    """نموذج إضافة/تعديل الدفعة"""
    student_id = SelectField('الطالب', coerce=int, validators=[DataRequired()])
    amount = FloatField('المبلغ', validators=[DataRequired(), NumberRange(min=0)])
    payment_type = SelectField('نوع الدفع', 
                              choices=[('monthly', 'شهري'), ('annual', 'سنوي')], 
                              validators=[DataRequired()])
    payment_date = DateField('تاريخ الدفع', default=date.today, validators=[DataRequired()])
    due_date = DateField('تاريخ الاستحقاق', validators=[DataRequired()])
    status = SelectField('الحالة', 
                        choices=[('pending', 'معلق'), ('paid', 'مدفوع'), ('overdue', 'متأخر')], 
                        validators=[DataRequired()])
    month = SelectField('الشهر', 
                       choices=[(i, f'الشهر {i}') for i in range(1, 13)], 
                       coerce=int, validators=[Optional()])
    year = IntegerField('السنة', validators=[DataRequired(), NumberRange(min=2020, max=2030)])
    notes = TextAreaField('ملاحظات', validators=[Optional(), Length(max=500)])

class GradeForm(FlaskForm):
    """نموذج إضافة/تعديل الدرجة"""
    student_id = SelectField('الطالب', coerce=int, validators=[DataRequired()])
    subject_id = SelectField('المادة', coerce=int, validators=[DataRequired()])
    exam_type = SelectField('نوع الامتحان', 
                           choices=[('امتحان شهري', 'امتحان شهري'), 
                                   ('نصف السنة', 'نصف السنة'), 
                                   ('نهاية السنة', 'نهاية السنة')], 
                           validators=[DataRequired()])
    grade = FloatField('الدرجة', validators=[DataRequired(), NumberRange(min=0)])
    max_grade = FloatField('الدرجة العظمى', default=100.0, validators=[DataRequired(), NumberRange(min=1)])
    exam_date = DateField('تاريخ الامتحان', validators=[DataRequired()])
    semester = SelectField('الفصل', 
                          choices=[('الفصل الأول', 'الفصل الأول'), ('الفصل الثاني', 'الفصل الثاني')], 
                          validators=[DataRequired()])
    academic_year = StringField('السنة الدراسية', validators=[DataRequired(), Length(max=10)])

class AttendanceForm(FlaskForm):
    """نموذج إضافة/تعديل الحضور"""
    student_id = SelectField('الطالب', coerce=int, validators=[DataRequired()])
    date = DateField('التاريخ', default=date.today, validators=[DataRequired()])
    status = SelectField('الحالة', 
                        choices=[('present', 'حاضر'), ('absent', 'غائب'), 
                                ('late', 'متأخر'), ('excused', 'غياب مبرر')], 
                        validators=[DataRequired()])
    notes = TextAreaField('ملاحظات', validators=[Optional(), Length(max=500)])

class SearchForm(FlaskForm):
    """نموذج البحث"""
    search_term = StringField('البحث', validators=[Optional(), Length(max=100)])
    search_type = SelectField('نوع البحث', 
                             choices=[('all', 'الكل'), ('students', 'الطلاب'), 
                                     ('classes', 'الصفوف'), ('payments', 'المدفوعات')], 
                             default='all')

class BulkAttendanceForm(FlaskForm):
    """نموذج تسجيل الحضور الجماعي"""
    class_id = SelectField('الصف', coerce=int, validators=[DataRequired()])
    date = DateField('التاريخ', default=date.today, validators=[DataRequired()])
    # سيتم إضافة حقول الطلاب ديناميكياً في الكود
