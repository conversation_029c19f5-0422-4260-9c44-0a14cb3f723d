# دليل استكشاف الأخطاء وإصلاحها 🔧

## مشكلة: حفظ التلميذ لا يعمل

### الأسباب المحتملة والحلول:

#### 1. مشكلة في النماذج (Forms)
**الأعراض:**
- النموذج لا يحفظ البيانات
- رسائل خطأ غير واضحة
- إعادة توجيه إلى نفس الصفحة

**الحل:**
```bash
# تشغيل اختبار النماذج
python test_web.py
```

#### 2. مشكلة CSRF Token
**الأعراض:**
- خطأ "CSRF token missing"
- النموذج يرفض الإرسال

**الحل:**
تأكد من وجود `{{ form.hidden_tag() }}` في النموذج:
```html
<form method="POST">
    {{ form.hidden_tag() }}
    <!-- باقي الحقول -->
</form>
```

#### 3. عدم وجود صفوف دراسية
**الأعراض:**
- رسالة "يجب إضافة صفوف دراسية أولاً"
- قائمة الصفوف فارغة في النموذج

**الحل:**
```bash
# إنشاء البيانات التجريبية
python init_data.py
```

#### 4. مشكلة في قاعدة البيانات
**الأعراض:**
- خطأ في الحفظ
- البيانات لا تظهر

**الحل:**
```bash
# إعادة إنشاء قاعدة البيانات
rm school_management.db
python init_data.py
```

#### 5. مشكلة في التحقق من صحة البيانات
**الأعراض:**
- النموذج يرفض البيانات الصحيحة
- رسائل خطأ في الحقول

**الحل:**
تحقق من:
- تاريخ الميلاد (يجب أن يكون في الماضي)
- رقم الهاتف (أرقام فقط)
- الحقول المطلوبة مملوءة

## اختبار سريع للنظام

### 1. اختبار قاعدة البيانات
```bash
python -c "from app import app, db; from models import Student, Class; 
with app.app_context(): 
    print('الصفوف:', Class.query.count()); 
    print('الطلاب:', Student.query.count())"
```

### 2. اختبار النماذج
```bash
python test_web.py
```

### 3. اختبار الخادم
```bash
python app.py
# ثم افتح http://localhost:5000
```

## مشاكل شائعة أخرى

### مشكلة: الخادم لا يعمل
**الحل:**
```bash
# تحقق من المنفذ
netstat -an | findstr :5000

# أو استخدم منفذ آخر
python -c "from app import app; app.run(port=5001)"
```

### مشكلة: التصميم لا يظهر
**الحل:**
- تحقق من اتصال الإنترنت (Bootstrap CDN)
- امسح ذاكرة التخزين المؤقت للمتصفح
- تحقق من ملف `static/css/custom.css`

### مشكلة: الأيقونات لا تظهر
**الحل:**
- تحقق من اتصال الإنترنت (Font Awesome CDN)
- تحديث المتصفح

### مشكلة: البيانات العربية لا تظهر صحيحة
**الحل:**
- تأكد من ترميز UTF-8
- تحقق من إعدادات قاعدة البيانات

## خطوات التشخيص

### 1. فحص الملفات الأساسية
```bash
# تحقق من وجود الملفات
ls -la app.py models.py forms.py
ls -la templates/
ls -la static/
```

### 2. فحص قاعدة البيانات
```bash
# تحقق من وجود قاعدة البيانات
ls -la *.db

# فحص محتوى قاعدة البيانات
python -c "from app import app, db; from models import *; 
with app.app_context(): 
    print('الجداول:', db.engine.table_names())"
```

### 3. فحص السجلات (Logs)
```bash
# تشغيل مع تفعيل السجلات
python app.py --debug
```

## إعادة تثبيت النظام

إذا فشلت جميع الحلول:

```bash
# 1. نسخ احتياطي من البيانات (إن وجدت)
cp school_management.db backup_school.db

# 2. حذف الملفات المؤقتة
rm -rf __pycache__/
rm *.pyc

# 3. إعادة تثبيت المتطلبات
pip install -r requirements.txt --force-reinstall

# 4. إعادة إنشاء قاعدة البيانات
rm school_management.db
python init_data.py

# 5. اختبار النظام
python test_web.py
```

## طلب المساعدة

عند طلب المساعدة، يرجى تقديم:

1. **رسالة الخطأ الكاملة**
2. **خطوات إعادة إنتاج المشكلة**
3. **نتيجة الاختبارات:**
   ```bash
   python test_web.py
   ```
4. **معلومات النظام:**
   ```bash
   python --version
   pip list | grep -i flask
   ```

## نصائح للوقاية

1. **اختبر النظام بانتظام:**
   ```bash
   python test_web.py
   ```

2. **احتفظ بنسخ احتياطية:**
   ```bash
   cp school_management.db backup_$(date +%Y%m%d).db
   ```

3. **راقب السجلات:**
   - تحقق من رسائل الخطأ في المتصفح (F12)
   - راقب سجلات الخادم

4. **حدث المتطلبات بانتظام:**
   ```bash
   pip install -r requirements.txt --upgrade
   ```

---

**💡 نصيحة:** إذا كانت المشكلة مستمرة، جرب النظام في متصفح آخر أو في وضع التصفح الخاص.
