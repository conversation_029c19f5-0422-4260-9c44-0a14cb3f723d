"""
ملف تشغيل النظام
"""
from app import app, db
from models import Student, Class, Subject, Payment, Grade, Attendance

if __name__ == '__main__':
    with app.app_context():
        # إنشاء قاعدة البيانات
        db.create_all()
        print("تم إنشاء قاعدة البيانات")
        
        # التحقق من وجود بيانات
        classes_count = Class.query.count()
        students_count = Student.query.count()
        
        print(f"عدد الصفوف: {classes_count}")
        print(f"عدد الطلاب: {students_count}")
        
        if classes_count == 0:
            print("لا توجد بيانات. يرجى تشغيل init_data.py أولاً")
    
    print("بدء تشغيل الخادم...")
    app.run(debug=True, host='0.0.0.0', port=5000)
