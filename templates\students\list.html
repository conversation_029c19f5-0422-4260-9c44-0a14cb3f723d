{% extends "base.html" %}

{% block title %}إدارة الطلاب - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-3">
                        <i class="fas fa-user-graduate me-2"></i>
                        إدارة الطلاب
                    </h1>
                    <p class="text-muted">عرض وإدارة جميع الطلاب المسجلين في المدرسة</p>
                </div>
                <div>
                    <a href="{{ url_for('add_student') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة طالب جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ search }}" placeholder="ابحث بالاسم أو اسم ولي الأمر">
                        </div>
                        <div class="col-md-4">
                            <label for="class" class="form-label">الصف</label>
                            <select class="form-select" id="class" name="class">
                                <option value="">جميع الصفوف</option>
                                {% for class in classes %}
                                    <option value="{{ class.id }}" 
                                            {% if class_filter == class.id|string %}selected{% endif %}>
                                        {{ class.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                            <a href="{{ url_for('students_list') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Students Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة الطلاب ({{ students.total }} طالب)
                    </h5>
                </div>
                <div class="card-body">
                    {% if students.items %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الرقم</th>
                                        <th>الاسم الكامل</th>
                                        <th>العمر</th>
                                        <th>الصف</th>
                                        <th>ولي الأمر</th>
                                        <th>هاتف ولي الأمر</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for student in students.items %}
                                        <tr>
                                            <td>{{ student.id }}</td>
                                            <td>
                                                <strong>{{ student.full_name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ student.gender }}</small>
                                            </td>
                                            <td>{{ student.age }} سنة</td>
                                            <td>
                                                <span class="badge bg-info">{{ student.student_class.name }}</span>
                                            </td>
                                            <td>{{ student.parent_name }}</td>
                                            <td>{{ student.parent_phone }}</td>
                                            <td>
                                                {% if student.is_active %}
                                                    <span class="badge bg-success">نشط</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ url_for('student_detail', id=student.id) }}" 
                                                       class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ url_for('edit_student', id=student.id) }}" 
                                                       class="btn btn-sm btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            title="حذف" onclick="confirmDelete({{ student.id }}, '{{ student.full_name }}')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if students.pages > 1 %}
                            <nav aria-label="تنقل الصفحات">
                                <ul class="pagination justify-content-center">
                                    {% if students.has_prev %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('students_list', page=students.prev_num, search=search, class=class_filter) }}">
                                                السابق
                                            </a>
                                        </li>
                                    {% endif %}
                                    
                                    {% for page_num in students.iter_pages() %}
                                        {% if page_num %}
                                            {% if page_num != students.page %}
                                                <li class="page-item">
                                                    <a class="page-link" href="{{ url_for('students_list', page=page_num, search=search, class=class_filter) }}">
                                                        {{ page_num }}
                                                    </a>
                                                </li>
                                            {% else %}
                                                <li class="page-item active">
                                                    <span class="page-link">{{ page_num }}</span>
                                                </li>
                                            {% endif %}
                                        {% else %}
                                            <li class="page-item disabled">
                                                <span class="page-link">...</span>
                                            </li>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if students.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('students_list', page=students.next_num, search=search, class=class_filter) }}">
                                                التالي
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد طلاب</h5>
                            <p class="text-muted">ابدأ بإضافة طلاب جدد للمدرسة</p>
                            <a href="{{ url_for('add_student') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة طالب جديد
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الطالب <strong id="studentName"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    هذا الإجراء لا يمكن التراجع عنه!
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(studentId, studentName) {
    document.getElementById('studentName').textContent = studentName;
    document.getElementById('deleteForm').action = '/students/' + studentId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
