# دليل البدء السريع - نظام إدارة المدرسة 🚀

## التشغيل السريع

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. إنشاء البيانات التجريبية (اختياري)
```bash
python init_data.py
```

### 3. تشغيل النظام
```bash
python start_server.py
```

### 4. فتح المتصفح
انتقل إلى: http://localhost:5000

## الميزات المتاحة حالياً ✅

### 📚 إدارة الطلاب
- ✅ عرض قائمة الطلاب مع البحث والتصفية
- ✅ إضافة طالب جديد
- ✅ تعديل بيانات الطالب
- ✅ عرض تفاصيل الطالب
- ✅ حذف الطالب (مع التحقق من البيانات المرتبطة)

### 🎓 إدارة الصفوف
- ✅ عرض قائمة الصفوف مع الإحصائيات
- ✅ إضافة صف جديد
- ✅ تعديل بيانات الصف
- ✅ عرض تفاصيل الصف
- ✅ متابعة عدد الطلاب والسعة

### 💰 إدارة المدفوعات
- ✅ عرض قائمة المدفوعات مع التصفية
- ✅ إضافة دفعة جديدة
- ✅ تعديل الدفعة
- ✅ متابعة حالة الدفع (مدفوع/معلق/متأخر)
- ✅ إحصائيات المدفوعات

### 🎨 واجهة المستخدم
- ✅ تصميم عربي متجاوب (RTL)
- ✅ ألوان وتدرجات جميلة
- ✅ أيقونات Font Awesome
- ✅ تأثيرات حركية
- ✅ إشعارات تفاعلية

## البيانات التجريبية 📊

عند تشغيل `python init_data.py` سيتم إنشاء:

- **6 صفوف دراسية** (ابتدائي، متوسط، ثانوي)
- **حوالي 70 طالب** موزعين على الصفوف
- **مواد دراسية** لكل صف حسب المرحلة
- **20 دفعة** مع حالات مختلفة
- **درجات** لبعض الطلاب
- **سجلات حضور** لأسبوع واحد

## الملفات المهمة 📁

```
ecole/
├── app.py              # التطبيق الرئيسي
├── models.py           # نماذج قاعدة البيانات
├── forms.py            # نماذج الويب
├── config.py           # إعدادات النظام
├── init_data.py        # البيانات التجريبية
├── start_server.py     # ملف التشغيل المحسن
├── requirements.txt    # المتطلبات
├── templates/          # قوالب HTML
├── static/            # الملفات الثابتة
└── school_management.db # قاعدة البيانات
```

## اختبار الميزات 🧪

### إضافة طالب جديد
1. انتقل إلى "إدارة الطلاب"
2. اضغط "إضافة طالب جديد"
3. املأ البيانات واحفظ

### إنشاء صف دراسي
1. انتقل إلى "إدارة الصفوف"
2. اضغط "إضافة صف جديد"
3. حدد المرحلة والرسوم

### تسجيل دفعة
1. انتقل إلى "المدفوعات"
2. اضغط "إضافة دفعة جديدة"
3. اختر الطالب والمبلغ

## الميزات قيد التطوير 🚧

- [ ] نظام الدرجات والنتائج
- [ ] نظام الحضور والغياب
- [ ] التقارير والإحصائيات المتقدمة
- [ ] نظام المستخدمين والصلاحيات
- [ ] طباعة الإيصالات والتقارير
- [ ] تصدير البيانات (Excel/PDF)
- [ ] إشعارات SMS/Email
- [ ] نسخ احتياطية

## حل المشاكل الشائعة 🔧

### المتطلبات غير مثبتة
```bash
pip install flask flask-sqlalchemy flask-wtf wtforms
```

### قاعدة البيانات لا تعمل
```bash
# احذف قاعدة البيانات وأعد إنشاءها
rm school_management.db
python init_data.py
```

### الخادم لا يعمل
```bash
# تأكد من أن المنفذ 5000 غير مستخدم
netstat -an | findstr :5000
```

### مشاكل في التصميم
- تأكد من وجود اتصال بالإنترنت (Bootstrap و Font Awesome)
- امسح ذاكرة التخزين المؤقت للمتصفح

## الدعم والمساعدة 💬

- راجع ملف `README.md` للتفاصيل الكاملة
- تحقق من ملف `config.py` للإعدادات
- راجع ملفات `templates/` لتخصيص الواجهة
- تحقق من ملف `static/css/custom.css` للتصميم

## نصائح للتطوير 💡

1. **استخدم البيانات التجريبية** لاختبار الميزات
2. **راجع ملف `models.py`** لفهم هيكل البيانات
3. **استخدم `forms.py`** لإضافة نماذج جديدة
4. **اتبع نمط الكود الموجود** عند إضافة ميزات
5. **اختبر على متصفحات مختلفة** للتأكد من التوافق

---

**مبروك! 🎉 نظام إدارة المدرسة جاهز للاستخدام**
