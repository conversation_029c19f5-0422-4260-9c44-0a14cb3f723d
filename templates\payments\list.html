{% extends "base.html" %}

{% block title %}إدارة المدفوعات - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-3">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        إدارة المدفوعات
                    </h1>
                    <p class="text-muted">متابعة وإدارة جميع مدفوعات الطلاب</p>
                </div>
                <div>
                    <a href="{{ url_for('add_payment') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة دفعة جديدة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card text-center">
                <div class="card-body">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h4>{{ payments.items|selectattr('status', 'equalto', 'paid')|list|length }}</h4>
                    <small>مدفوعة</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card-2 text-center">
                <div class="card-body">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <h4>{{ payments.items|selectattr('status', 'equalto', 'pending')|list|length }}</h4>
                    <small>معلقة</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card-3 text-center">
                <div class="card-body">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <h4>{{ payments.items|selectattr('status', 'equalto', 'overdue')|list|length }}</h4>
                    <small>متأخرة</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card-4 text-center">
                <div class="card-body">
                    <i class="fas fa-coins fa-2x mb-2"></i>
                    <h4>{{ payments.total }}</h4>
                    <small>إجمالي المدفوعات</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="student" class="form-label">البحث عن طالب</label>
                            <input type="text" class="form-control" id="student" name="student" 
                                   value="{{ student_filter }}" placeholder="ابحث بالاسم">
                        </div>
                        <div class="col-md-4">
                            <label for="status" class="form-label">حالة الدفع</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="paid" {% if status_filter == 'paid' %}selected{% endif %}>مدفوع</option>
                                <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>معلق</option>
                                <option value="overdue" {% if status_filter == 'overdue' %}selected{% endif %}>متأخر</option>
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                            <a href="{{ url_for('payments_list') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Payments Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة المدفوعات ({{ payments.total }} دفعة)
                    </h5>
                </div>
                <div class="card-body">
                    {% if payments.items %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الرقم</th>
                                        <th>الطالب</th>
                                        <th>المبلغ</th>
                                        <th>النوع</th>
                                        <th>تاريخ الدفع</th>
                                        <th>تاريخ الاستحقاق</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for payment in payments.items %}
                                        <tr class="{% if payment.is_overdue %}table-danger{% endif %}">
                                            <td>{{ payment.id }}</td>
                                            <td>
                                                <strong>{{ payment.student.full_name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ payment.student.student_class.name }}</small>
                                            </td>
                                            <td>
                                                <strong class="text-success">{{ payment.amount }} د.ج</strong>
                                            </td>
                                            <td>
                                                {% if payment.payment_type == 'monthly' %}
                                                    <span class="badge bg-info">شهري</span>
                                                    {% if payment.month %}
                                                        <br><small>الشهر {{ payment.month }}</small>
                                                    {% endif %}
                                                {% else %}
                                                    <span class="badge bg-primary">سنوي</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ payment.payment_date.strftime('%Y-%m-%d') }}</td>
                                            <td>
                                                {{ payment.due_date.strftime('%Y-%m-%d') }}
                                                {% if payment.is_overdue %}
                                                    <br><small class="text-danger">متأخر</small>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if payment.status == 'paid' %}
                                                    <span class="badge bg-success">مدفوع</span>
                                                {% elif payment.status == 'pending' %}
                                                    <span class="badge bg-warning">معلق</span>
                                                {% else %}
                                                    <span class="badge bg-danger">متأخر</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ url_for('edit_payment', id=payment.id) }}" 
                                                       class="btn btn-sm btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    {% if payment.status == 'pending' %}
                                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                                title="تأكيد الدفع" onclick="confirmPayment({{ payment.id }})">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    {% endif %}
                                                    <button type="button" class="btn btn-sm btn-outline-info" 
                                                            title="طباعة إيصال" onclick="printReceipt({{ payment.id }})">
                                                        <i class="fas fa-print"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if payments.pages > 1 %}
                            <nav aria-label="تنقل الصفحات">
                                <ul class="pagination justify-content-center">
                                    {% if payments.has_prev %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('payments_list', page=payments.prev_num, status=status_filter, student=student_filter) }}">
                                                السابق
                                            </a>
                                        </li>
                                    {% endif %}
                                    
                                    {% for page_num in payments.iter_pages() %}
                                        {% if page_num %}
                                            {% if page_num != payments.page %}
                                                <li class="page-item">
                                                    <a class="page-link" href="{{ url_for('payments_list', page=page_num, status=status_filter, student=student_filter) }}">
                                                        {{ page_num }}
                                                    </a>
                                                </li>
                                            {% else %}
                                                <li class="page-item active">
                                                    <span class="page-link">{{ page_num }}</span>
                                                </li>
                                            {% endif %}
                                        {% else %}
                                            <li class="page-item disabled">
                                                <span class="page-link">...</span>
                                            </li>
                                        {% endif %}
                                    {% endfor %}
                                    
                                    {% if payments.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('payments_list', page=payments.next_num, status=status_filter, student=student_filter) }}">
                                                التالي
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد مدفوعات</h5>
                            <p class="text-muted">ابدأ بإضافة مدفوعات الطلاب</p>
                            <a href="{{ url_for('add_payment') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة دفعة جديدة
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmPayment(paymentId) {
    if (confirm('هل تريد تأكيد هذه الدفعة؟')) {
        // هنا يمكن إضافة AJAX لتحديث حالة الدفع
        alert('سيتم تطوير هذه الميزة قريباً');
    }
}

function printReceipt(paymentId) {
    alert('سيتم تطوير ميزة طباعة الإيصالات قريباً');
}
</script>
{% endblock %}
