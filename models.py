from datetime import datetime, date
from flask_sqlalchemy import SQLAlchemy

db = SQLAlchemy()

class Student(db.Model):
    """نموذج الطالب"""
    __tablename__ = 'students'
    
    id = db.Column(db.Integer, primary_key=True)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    date_of_birth = db.Column(db.Date, nullable=False)
    gender = db.Column(db.String(10), nullable=False)  # ذكر/أنثى
    address = db.Column(db.Text)
    phone = db.Column(db.String(20))
    parent_phone = db.Column(db.String(20), nullable=False)
    parent_name = db.Column(db.String(100), nullable=False)
    enrollment_date = db.Column(db.Date, default=date.today)
    is_active = db.Column(db.<PERSON>, default=True)
    
    # العلاقات
    class_id = db.Column(db.Integer, db.<PERSON>Key('classes.id'), nullable=False)
    payments = db.relationship('Payment', backref='student', lazy=True)
    grades = db.relationship('Grade', backref='student', lazy=True)
    attendances = db.relationship('Attendance', backref='student', lazy=True)
    
    def __repr__(self):
        return f'<Student {self.first_name} {self.last_name}>'
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def age(self):
        today = date.today()
        return today.year - self.date_of_birth.year - ((today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day))

class Class(db.Model):
    """نموذج الصف الدراسي"""
    __tablename__ = 'classes'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False, unique=True)  # مثل: الصف الأول الابتدائي
    level = db.Column(db.String(20), nullable=False)  # ابتدائي، متوسط، ثانوي
    academic_year = db.Column(db.String(10), nullable=False)  # 2023-2024
    monthly_fee = db.Column(db.Float, nullable=False, default=0.0)
    annual_fee = db.Column(db.Float, nullable=False, default=0.0)
    max_students = db.Column(db.Integer, default=30)
    
    # العلاقات
    students = db.relationship('Student', backref='student_class', lazy=True)
    subjects = db.relationship('Subject', backref='class_subjects', lazy=True)
    
    def __repr__(self):
        return f'<Class {self.name}>'
    
    @property
    def current_students_count(self):
        return Student.query.filter_by(class_id=self.id, is_active=True).count()

class Subject(db.Model):
    """نموذج المادة الدراسية"""
    __tablename__ = 'subjects'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(10), unique=True)
    class_id = db.Column(db.Integer, db.ForeignKey('classes.id'), nullable=False)
    teacher_name = db.Column(db.String(100))
    
    # العلاقات
    grades = db.relationship('Grade', backref='subject', lazy=True)
    
    def __repr__(self):
        return f'<Subject {self.name}>'

class Payment(db.Model):
    """نموذج المدفوعات"""
    __tablename__ = 'payments'
    
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    payment_type = db.Column(db.String(20), nullable=False)  # monthly/annual
    payment_date = db.Column(db.Date, default=date.today)
    due_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending/paid/overdue
    month = db.Column(db.Integer)  # للدفع الشهري (1-12)
    year = db.Column(db.Integer, nullable=False)
    notes = db.Column(db.Text)
    
    def __repr__(self):
        return f'<Payment {self.amount} for {self.student.full_name}>'
    
    @property
    def is_overdue(self):
        return self.status == 'pending' and self.due_date < date.today()

class Grade(db.Model):
    """نموذج الدرجات"""
    __tablename__ = 'grades'
    
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'), nullable=False)
    subject_id = db.Column(db.Integer, db.ForeignKey('subjects.id'), nullable=False)
    exam_type = db.Column(db.String(50), nullable=False)  # امتحان شهري، نصف السنة، نهاية السنة
    grade = db.Column(db.Float, nullable=False)
    max_grade = db.Column(db.Float, default=100.0)
    exam_date = db.Column(db.Date, nullable=False)
    semester = db.Column(db.String(20), nullable=False)  # الفصل الأول/الثاني
    academic_year = db.Column(db.String(10), nullable=False)
    
    def __repr__(self):
        return f'<Grade {self.grade}/{self.max_grade} for {self.student.full_name}>'
    
    @property
    def percentage(self):
        return (self.grade / self.max_grade) * 100 if self.max_grade > 0 else 0

class Attendance(db.Model):
    """نموذج الحضور والغياب"""
    __tablename__ = 'attendance'
    
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'), nullable=False)
    date = db.Column(db.Date, nullable=False, default=date.today)
    status = db.Column(db.String(20), nullable=False)  # present/absent/late/excused
    notes = db.Column(db.Text)
    
    # فهرس مركب لمنع التكرار
    __table_args__ = (db.UniqueConstraint('student_id', 'date', name='unique_student_date'),)
    
    def __repr__(self):
        return f'<Attendance {self.student.full_name} - {self.date} - {self.status}>'
