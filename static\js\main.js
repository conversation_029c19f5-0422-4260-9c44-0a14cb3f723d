/**
 * JavaScript مخصص لنظام إدارة المدرسة
 */

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeSystem();
});

/**
 * تهيئة النظام
 */
function initializeSystem() {
    // تفعيل التلميحات
    initializeTooltips();
    
    // تفعيل النوافذ المنبثقة
    initializeModals();
    
    // تحسين النماذج
    enhanceForms();
    
    // تفعيل البحث المباشر
    initializeLiveSearch();
    
    // تحسين الجداول
    enhanceTables();
    
    // تفعيل الإشعارات
    initializeNotifications();
    
    // تحديث الوقت
    updateDateTime();
    setInterval(updateDateTime, 60000); // كل دقيقة
}

/**
 * تفعيل التلميحات
 */
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * تفعيل النوافذ المنبثقة
 */
function initializeModals() {
    // إضافة تأثيرات للنوافذ المنبثقة
    var modals = document.querySelectorAll('.modal');
    modals.forEach(function(modal) {
        modal.addEventListener('show.bs.modal', function() {
            document.body.style.overflow = 'hidden';
        });
        
        modal.addEventListener('hidden.bs.modal', function() {
            document.body.style.overflow = 'auto';
        });
    });
}

/**
 * تحسين النماذج
 */
function enhanceForms() {
    // تحسين حقول الإدخال
    var inputs = document.querySelectorAll('.form-control, .form-select');
    inputs.forEach(function(input) {
        // إضافة تأثير التركيز
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
        
        // التحقق من صحة البيانات
        input.addEventListener('input', function() {
            validateField(this);
        });
    });
    
    // تحسين أزرار الإرسال
    var submitButtons = document.querySelectorAll('button[type="submit"]');
    submitButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            var form = this.closest('form');
            if (form && !validateForm(form)) {
                e.preventDefault();
                showNotification('يرجى تصحيح الأخطاء في النموذج', 'error');
            } else {
                // إضافة مؤشر التحميل
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
                this.disabled = true;
            }
        });
    });
}

/**
 * التحقق من صحة حقل واحد
 */
function validateField(field) {
    var isValid = true;
    var errorMessage = '';
    
    // التحقق من الحقول المطلوبة
    if (field.hasAttribute('required') && !field.value.trim()) {
        isValid = false;
        errorMessage = 'هذا الحقل مطلوب';
    }
    
    // التحقق من البريد الإلكتروني
    if (field.type === 'email' && field.value) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(field.value)) {
            isValid = false;
            errorMessage = 'يرجى إدخال بريد إلكتروني صحيح';
        }
    }
    
    // التحقق من أرقام الهاتف
    if (field.name && field.name.includes('phone') && field.value) {
        var phoneRegex = /^[0-9+\-\s()]+$/;
        if (!phoneRegex.test(field.value)) {
            isValid = false;
            errorMessage = 'يرجى إدخال رقم هاتف صحيح';
        }
    }
    
    // عرض النتيجة
    if (isValid) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
    } else {
        field.classList.remove('is-valid');
        field.classList.add('is-invalid');
        
        // إضافة رسالة الخطأ
        var feedback = field.parentElement.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.textContent = errorMessage;
        }
    }
    
    return isValid;
}

/**
 * التحقق من صحة النموذج كاملاً
 */
function validateForm(form) {
    var isValid = true;
    var inputs = form.querySelectorAll('.form-control, .form-select');
    
    inputs.forEach(function(input) {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * تفعيل البحث المباشر
 */
function initializeLiveSearch() {
    var searchInputs = document.querySelectorAll('input[name="search"]');
    searchInputs.forEach(function(input) {
        var timeout;
        input.addEventListener('input', function() {
            clearTimeout(timeout);
            timeout = setTimeout(function() {
                // يمكن إضافة البحث المباشر هنا
                console.log('البحث عن:', input.value);
            }, 500);
        });
    });
}

/**
 * تحسين الجداول
 */
function enhanceTables() {
    var tables = document.querySelectorAll('.table');
    tables.forEach(function(table) {
        // إضافة تأثيرات للصفوف
        var rows = table.querySelectorAll('tbody tr');
        rows.forEach(function(row) {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.01)';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });
    });
}

/**
 * تفعيل الإشعارات
 */
function initializeNotifications() {
    // إخفاء الإشعارات تلقائياً بعد 5 ثوان
    var alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            if (alert.parentElement) {
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-20px)';
                setTimeout(function() {
                    if (alert.parentElement) {
                        alert.remove();
                    }
                }, 300);
            }
        }, 5000);
    });
}

/**
 * عرض إشعار مخصص
 */
function showNotification(message, type = 'info', duration = 5000) {
    var alertClass = 'alert-' + type;
    var iconClass = {
        'success': 'fa-check-circle',
        'error': 'fa-exclamation-triangle',
        'warning': 'fa-exclamation-circle',
        'info': 'fa-info-circle'
    }[type] || 'fa-info-circle';
    
    var alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            <i class="fas ${iconClass} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', alertHtml);
    
    // إزالة الإشعار تلقائياً
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert[style*="position: fixed"]');
        if (alerts.length > 0) {
            var lastAlert = alerts[alerts.length - 1];
            lastAlert.style.opacity = '0';
            lastAlert.style.transform = 'translateX(100%)';
            setTimeout(function() {
                if (lastAlert.parentElement) {
                    lastAlert.remove();
                }
            }, 300);
        }
    }, duration);
}

/**
 * تحديث التاريخ والوقت
 */
function updateDateTime() {
    var now = new Date();
    var options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        weekday: 'long'
    };
    
    var dateTimeString = now.toLocaleDateString('ar-SA', options);
    
    var dateTimeElements = document.querySelectorAll('.current-datetime');
    dateTimeElements.forEach(function(element) {
        element.textContent = dateTimeString;
    });
}

/**
 * تأكيد الحذف
 */
function confirmDelete(itemName, itemType = 'العنصر') {
    return confirm(`هل أنت متأكد من حذف ${itemType}: ${itemName}؟\n\nهذا الإجراء لا يمكن التراجع عنه!`);
}

/**
 * تصدير البيانات
 */
function exportData(format, data) {
    if (format === 'excel') {
        showNotification('سيتم تطوير ميزة التصدير إلى Excel قريباً', 'info');
    } else if (format === 'pdf') {
        showNotification('سيتم تطوير ميزة التصدير إلى PDF قريباً', 'info');
    }
}

/**
 * طباعة الصفحة
 */
function printPage() {
    window.print();
}

/**
 * تحديث الصفحة
 */
function refreshPage() {
    location.reload();
}

/**
 * العودة للصفحة السابقة
 */
function goBack() {
    if (window.history.length > 1) {
        window.history.back();
    } else {
        window.location.href = '/';
    }
}

/**
 * تبديل وضع الشاشة الكاملة
 */
function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        }
    }
}

/**
 * حفظ البيانات محلياً
 */
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (e) {
        console.error('خطأ في حفظ البيانات:', e);
        return false;
    }
}

/**
 * استرجاع البيانات المحفوظة محلياً
 */
function getFromLocalStorage(key) {
    try {
        var data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (e) {
        console.error('خطأ في استرجاع البيانات:', e);
        return null;
    }
}

// تصدير الدوال للاستخدام العام
window.SchoolManagement = {
    showNotification,
    confirmDelete,
    exportData,
    printPage,
    refreshPage,
    goBack,
    toggleFullscreen,
    saveToLocalStorage,
    getFromLocalStorage
};
