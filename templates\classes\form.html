{% extends "base.html" %}

{% block title %}{{ title }} - نظام إدارة المدرسة{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-3">
                        <i class="fas fa-chalkboard me-2"></i>
                        {{ title }}
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                            <li class="breadcrumb-item"><a href="{{ url_for('classes_list') }}">الصفوف</a></li>
                            <li class="breadcrumb-item active">{{ title }}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ url_for('classes_list') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>
                        بيانات الصف
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" novalidate>
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <!-- اسم الصف -->
                            <div class="col-md-6 mb-3">
                                {{ form.name.label(class="form-label") }}
                                {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">مثال: الصف الأول الابتدائي</div>
                            </div>

                            <!-- المرحلة -->
                            <div class="col-md-6 mb-3">
                                {{ form.level.label(class="form-label") }}
                                {{ form.level(class="form-select" + (" is-invalid" if form.level.errors else "")) }}
                                {% if form.level.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.level.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <!-- السنة الدراسية -->
                            <div class="col-md-6 mb-3">
                                {{ form.academic_year.label(class="form-label") }}
                                {{ form.academic_year(class="form-control" + (" is-invalid" if form.academic_year.errors else "")) }}
                                {% if form.academic_year.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.academic_year.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">مثال: 2024-2025</div>
                            </div>

                            <!-- الحد الأقصى للطلاب -->
                            <div class="col-md-6 mb-3">
                                {{ form.max_students.label(class="form-label") }}
                                {{ form.max_students(class="form-control" + (" is-invalid" if form.max_students.errors else "")) }}
                                {% if form.max_students.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.max_students.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <!-- الرسوم الشهرية -->
                            <div class="col-md-6 mb-3">
                                {{ form.monthly_fee.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.monthly_fee(class="form-control" + (" is-invalid" if form.monthly_fee.errors else "")) }}
                                    <span class="input-group-text">د.ج</span>
                                </div>
                                {% if form.monthly_fee.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.monthly_fee.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- الرسوم السنوية -->
                            <div class="col-md-6 mb-3">
                                {{ form.annual_fee.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.annual_fee(class="form-control" + (" is-invalid" if form.annual_fee.errors else "")) }}
                                    <span class="input-group-text">د.ج</span>
                                </div>
                                {% if form.annual_fee.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.annual_fee.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- معاينة الرسوم -->
                        <div class="alert alert-info">
                            <h6><i class="fas fa-calculator me-2"></i>معاينة الرسوم:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>الرسوم الشهرية:</strong> <span id="monthly-preview">0</span> د.ج
                                </div>
                                <div class="col-md-6">
                                    <strong>الرسوم السنوية:</strong> <span id="annual-preview">0</span> د.ج
                                </div>
                            </div>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                الرسوم السنوية عادة ما تكون أقل من 12 × الرسوم الشهرية (خصم للدفع المقدم)
                            </small>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    {% if class_obj %}تحديث البيانات{% else %}إضافة الصف{% endif %}
                                </button>
                                <a href="{{ url_for('classes_list') }}" class="btn btn-secondary ms-2">
                                    <i class="fas fa-times me-2"></i>
                                    إلغاء
                                </a>
                            </div>
                            {% if class_obj %}
                                <div>
                                    <a href="{{ url_for('class_detail', id=class_obj.id) }}" class="btn btn-info">
                                        <i class="fas fa-eye me-2"></i>
                                        عرض التفاصيل
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // التركيز على أول حقل
    const firstInput = document.querySelector('input[type="text"]');
    if (firstInput) {
        firstInput.focus();
    }
    
    // معاينة الرسوم
    const monthlyFeeInput = document.querySelector('input[name="monthly_fee"]');
    const annualFeeInput = document.querySelector('input[name="annual_fee"]');
    const monthlyPreview = document.getElementById('monthly-preview');
    const annualPreview = document.getElementById('annual-preview');
    
    function updatePreview() {
        const monthlyFee = parseFloat(monthlyFeeInput.value) || 0;
        const annualFee = parseFloat(annualFeeInput.value) || 0;
        
        monthlyPreview.textContent = monthlyFee.toLocaleString();
        annualPreview.textContent = annualFee.toLocaleString();
        
        // اقتراح الرسوم السنوية
        if (monthlyFee > 0 && annualFee === 0) {
            const suggestedAnnual = monthlyFee * 10; // خصم شهرين
            annualFeeInput.value = suggestedAnnual;
            annualPreview.textContent = suggestedAnnual.toLocaleString();
        }
    }
    
    if (monthlyFeeInput && annualFeeInput) {
        monthlyFeeInput.addEventListener('input', updatePreview);
        annualFeeInput.addEventListener('input', updatePreview);
        
        // تحديث أولي
        updatePreview();
    }
    
    // تنسيق أرقام الرسوم
    const feeInputs = document.querySelectorAll('input[name="monthly_fee"], input[name="annual_fee"]');
    feeInputs.forEach(input => {
        input.addEventListener('input', function() {
            // إزالة جميع الأحرف غير الرقمية والنقطة
            this.value = this.value.replace(/[^\d.]/g, '');
        });
    });
});
</script>
{% endblock %}
