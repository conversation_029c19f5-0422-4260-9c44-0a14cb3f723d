# متطلبات نظام إدارة المدرسة

# إطار العمل الأساسي
Flask==2.3.3
Werkzeug==2.3.7

# قاعدة البيانات
Flask-SQLAlchemy==3.0.5

# النماذج والتحقق
Flask-WTF==1.1.1
WTForms==3.0.1

# التواريخ والأوقات
python-dateutil==2.8.2

# المستقبل - المصادقة والتفويض
Flask-Login==0.6.3

# المستقبل - التقارير والتصدير
reportlab==4.0.4
openpyxl==3.1.2

# المستقبل - البريد الإلكتروني
Flask-Mail==0.9.1

# المستقبل - التخزين المؤقت
Flask-Caching==2.1.0
