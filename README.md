# نظام إدارة المدرسة 🏫

نظام شامل لإدارة المدارس مطور بـ Python و Flask، يوفر جميع الأدوات اللازمة لإدارة الطلاب والصفوف والمدفوعات والدرجات والحضور.

## المميزات الرئيسية ✨

### 📚 إدارة الطلاب
- إضافة وتعديل وحذف بيانات الطلاب
- تتبع المعلومات الشخصية وبيانات الاتصال
- ربط الطلاب بالصفوف الدراسية
- عرض تفاصيل شاملة لكل طالب

### 🎓 إدارة الصفوف والسنوات الدراسية
- إنشاء وإدارة الصفوف الدراسية
- تحديد الرسوم الشهرية والسنوية لكل صف
- متابعة عدد الطلاب والسعة القصوى
- تنظيم المواد الدراسية لكل صف

### 💰 نظام المدفوعات
- تسجيل المدفوعات الشهرية والسنوية
- متابعة حالة الدفع (مدفوع/معلق/متأخر)
- تنبيهات للمدفوعات المتأخرة
- تقارير مالية شاملة

### 📊 نظام الدرجات والنتائج
- إدخال درجات الامتحانات المختلفة
- حساب النسب المئوية تلقائياً
- تتبع الأداء الأكاديمي للطلاب
- تقارير الدرجات حسب المادة والفصل

### 📅 نظام الحضور والغياب
- تسجيل الحضور اليومي
- متابعة معدلات الغياب
- تصنيف أنواع الغياب (مبرر/غير مبرر)
- تقارير الحضور الشهرية

### 📈 التقارير والإحصائيات
- لوحة تحكم شاملة
- إحصائيات في الوقت الفعلي
- تقارير قابلة للطباعة
- تصدير البيانات

## التقنيات المستخدمة 🛠️

- **Backend**: Python 3.8+, Flask
- **Database**: SQLAlchemy (SQLite)
- **Frontend**: HTML5, CSS3, JavaScript
- **UI Framework**: Bootstrap 5 (RTL)
- **Icons**: Font Awesome
- **Forms**: Flask-WTF, WTForms

## متطلبات التشغيل 📋

```bash
Python 3.8+
Flask 2.3+
Flask-SQLAlchemy 3.0+
Flask-WTF 1.1+
WTForms 3.0+
```

## التثبيت والتشغيل 🚀

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd ecole
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. إنشاء البيانات التجريبية (اختياري)
```bash
python init_data.py
```

### 4. تشغيل النظام
```bash
python app.py
```

### 5. فتح المتصفح
```
http://localhost:5000
```

## هيكل المشروع 📁

```
ecole/
├── app.py                 # التطبيق الرئيسي
├── models.py             # نماذج قاعدة البيانات
├── forms.py              # نماذج الويب
├── init_data.py          # البيانات التجريبية
├── requirements.txt      # المتطلبات
├── templates/            # قوالب HTML
│   ├── base.html
│   ├── index.html
│   ├── students/
│   ├── classes/
│   └── payments/
└── static/              # الملفات الثابتة
```

## قاعدة البيانات 🗄️

النظام يستخدم SQLite كقاعدة بيانات افتراضية مع الجداول التالية:

- **students**: بيانات الطلاب
- **classes**: الصفوف الدراسية
- **subjects**: المواد الدراسية
- **payments**: المدفوعات
- **grades**: الدرجات
- **attendance**: الحضور والغياب

## الاستخدام 📖

### إضافة طالب جديد
1. انتقل إلى "إدارة الطلاب"
2. اضغط على "إضافة طالب جديد"
3. املأ البيانات المطلوبة
4. احفظ البيانات

### إنشاء صف دراسي
1. انتقل إلى "إدارة الصفوف"
2. اضغط على "إضافة صف جديد"
3. حدد المرحلة والرسوم
4. احفظ البيانات

### تسجيل دفعة
1. انتقل إلى "المدفوعات"
2. اضغط على "إضافة دفعة جديدة"
3. اختر الطالب والمبلغ
4. حدد نوع الدفع (شهري/سنوي)

## المميزات المستقبلية 🔮

- [ ] نظام المستخدمين والصلاحيات
- [ ] إشعارات SMS/Email
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة الدفع الإلكتروني
- [ ] تقارير متقدمة مع الرسوم البيانية
- [ ] نسخ احتياطية تلقائية
- [ ] دعم متعدد اللغات

## المساهمة 🤝

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إضافة التحسينات
4. إرسال Pull Request

## الدعم 💬

للحصول على الدعم أو الإبلاغ عن مشاكل:

- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني

## الترخيص 📄

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## شكر خاص 🙏

- Bootstrap لواجهة المستخدم الجميلة
- Font Awesome للأيقونات الرائعة
- Flask لإطار العمل المرن
- المجتمع العربي للمطورين

---

**تم تطوير هذا النظام بـ ❤️ لخدمة التعليم في الوطن العربي**
